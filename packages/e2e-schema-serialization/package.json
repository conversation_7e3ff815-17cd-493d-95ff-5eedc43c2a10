{"name": "@ad/e2e-schema-serialization", "version": "1.0.0", "description": "E2E Schema 序列化与差异计算工具", "repository": {"type": "git", "url": "*************************:ks-ad/ad-fe/grandcanal/canal.git"}, "license": "MIT", "author": "AD", "sideEffects": false, "main": "dist/index.cjs.js", "module": "dist/index.es.js", "types": "dist/index.d.ts", "scripts": {"build": "rm -rf dist && rollup --config rollup.config.ts --configPlugin typescript", "start": "rollup --config rollup.config.ts --configPlugin typescript --watch", "test": "jest --passWithNoTests", "test:w": "jest --watch"}, "dependencies": {"@ad/e2e-schema": "workspace:^", "type-fest": "^4.6.0"}, "devDependencies": {"@rollup/plugin-terser": "^0.4.3", "@rollup/plugin-typescript": "^11.1.4", "@types/jest": "^29.5.5", "@types/lodash": "^4.14.200", "jest": "^29.7.0", "rollup": "^3.29.3", "rollup-plugin-exclude-dependencies-from-bundle": "1.1.23", "ts-jest": "^29.1.1"}}