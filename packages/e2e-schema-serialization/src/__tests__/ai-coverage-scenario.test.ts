/**
 * 🎯 AI覆盖率计算真实场景测试
 *
 * 基于实际开发流程的完整测试场景：
 *
 * 📖 场景描述：
 * 一个变更alpha包含5个修改操作：A、B、C、D、E
 * - initV → AV：AI修改A（添加用户头像组件）
 * - AV → BV：人工修改B（调整布局样式）
 * - BV → CV：人工修改C（添加提交按钮）
 * - CV → DV：AI修改D（添加表单验证逻辑）
 * - DV → EV：人工修改E（优化错误提示）
 *
 * 🧮 计算逻辑：
 * 1. alphaDiff = diff(initV, EV) - 整个变更的总修改量
 * 2. ADiff = diff(initV, AV) - AI修改A的贡献
 * 3. DDiff = diff(CV, DV) - AI修改D的贡献
 * 4. 通过消耗式匹配计算 (ADiff + DDiff) 在 alphaDiff 中的占比
 */

import type { E2ESchema } from '@ad/e2e-schema'
import { CoverageCalculator } from '../core/coverage'
import { SchemaDiffEngine } from '../core/engine'

describe('🎯 AI覆盖率计算真实场景', () => {
  let engine: SchemaDiffEngine

  beforeEach(() => {
    engine = new SchemaDiffEngine()
  })

  describe('📖 用户注册表单开发流程', () => {
    // 测试数据：模拟真实开发中的Schema演进过程
    const initV: E2ESchema = {
      schemaVersion: '1.0.0',
      version: '1.0.0',
      view: {
        id: 'registerForm',
        type: 'Form',
        name: '用户注册表单',
        props: { layout: 'vertical' },
        children: [
          {
            id: 'usernameInput',
            type: 'Input',
            name: '用户名',
            props: { placeholder: '请输入用户名' },
            effect: {
              type: 'js',
              code: 'console.log("username input mounted")',
              codeTS: 'console.log("username input mounted");',
              codeES: 'console.log("username input mounted");',
            },
          },
          {
            id: 'emailInput',
            type: 'Input',
            name: '邮箱',
            props: { placeholder: '请输入邮箱', type: 'email' },
            if: {
              type: 'js',
              code: 'return showEmailField',
              codeTS: 'return showEmailField as boolean',
            },
            apis: [
              {
                id: 'validateEmail',
                name: '邮箱验证',
                type: 'rpc',
                service: '/api/validate/email',
              },
            ],
          },
        ],
      },
      apis: [
        {
          id: 'register',
          name: '用户注册',
          type: 'rpc',
          service: '/api/user/register',
        },
      ],
      model: {
        code: `var UserForm = {};`,
        codeTS: `interface UserForm {
  username: string;
  email: string;
}`,
      },
    }

    // AV：AI添加头像组件
    // 手动计算变更数：initV → AV = 9个变更
    // - avatarUpload组件: type(add), name(add), props.accept(add), props.maxSize(add), props.placeholder(add), _parent_(add) = 6个
    // - usernameInput: _parent_(modify) = 1个 (父级children数组变化)
    // - emailInput: _parent_(modify) = 1个 (父级children数组变化)
    // - model.codeTS: avatar字段(add) = 1个
    const AV: E2ESchema = {
      ...initV,
      view: {
        ...initV.view,
        children: [
          {
            id: 'avatarUpload',
            type: 'Upload',
            name: '头像上传',
            props: {
              accept: 'image/*',
              maxSize: 2048,
              placeholder: '点击上传头像',
            },
          },
          ...(initV.view.children || []),
        ],
      },
      model: {
        code: `var UserForm = {};`,
        codeTS: `interface UserForm {
  username: string;
  email: string;
  avatar?: string;
}`,
      },
    }

    it('📊 验证 initV → AV 变更数量 (AI修改A)', () => {
      const diff = engine.diff(initV, AV)

      console.log('\n=== initV → AV 变更验证 ===')
      console.log('实际变更数:', diff.stats.total)
      console.log('按字段分布:', diff.stats.byField)
      console.log('按策略分布:', diff.stats.byStrategy)

      // 验证总变更数
      expect(diff.stats.total).toBe(9)
      expect(diff.stats.byField.view || 0).toBe(8) // View变更
      expect(diff.stats.byField.model || 0).toBe(1) // Model变更
    })

    // BV：人工调整布局
    // 手动计算变更数：AV → BV = 2个变更
    // - props.layout: 'vertical' → 'horizontal' (modify) = 1个
    // - props.style: undefined → 'margin: 10px' (add) = 1个
    const BV: E2ESchema = {
      ...AV,
      view: {
        ...AV.view,
        props: {
          layout: 'horizontal',
          style: 'margin: 10px',
        },
      },
    }

    it('📊 验证 AV → BV 变更数量 (人工修改B)', () => {
      const diff = engine.diff(AV, BV)

      console.log('\n=== AV → BV 变更验证 ===')
      console.log('实际变更数:', diff.stats.total)
      console.log('按字段分布:', diff.stats.byField)

      expect(diff.stats.total).toBe(2)
      expect(diff.stats.byField.view || 0).toBe(2) // View变更
    })

    // CV：人工添加按钮
    // 手动计算变更数：BV → CV = 6个变更
    // - submitButton组件: type(add), name(add), props.type(add), props.text(add), props.htmlType(add), _parent_(add) = 6个
    const CV: E2ESchema = {
      ...BV,
      view: {
        ...BV.view,
        children: [
          ...(BV.view.children || []),
          {
            id: 'submitButton',
            type: 'Button',
            name: '注册按钮',
            props: {
              type: 'primary',
              text: '立即注册',
              htmlType: 'submit',
            },
          },
        ],
      },
    }

    it('📊 验证 BV → CV 变更数量 (人工修改C)', () => {
      const diff = engine.diff(BV, CV)

      console.log('\n=== BV → CV 变更验证 ===')
      console.log('实际变更数:', diff.stats.total)
      console.log('按字段分布:', diff.stats.byField)
      console.log('按策略分布:', diff.stats.byStrategy)

      expect(diff.stats.total).toBe(6)
      expect(diff.stats.byField.view || 0).toBe(6) // View变更
    })

    // DV：AI添加验证逻辑
    // 手动计算变更数：CV → DV = 15个变更
    // Model变更(11个)：
    //   - code: 从"var UserForm = {};" → "var UserForm = {}; var validateForm = function() {};" = 1个整体变更
    //   - codeTS按行计算(CodeLineDiffStrategy忽略空行)：
    //     * 原4行内容相同，不产生change
    //     * 第5行空行被忽略
    //     * 第6-18行: 注释2行 + 函数定义11行 - 空行3行 = 10个新增行
    // APIs变更(4个)：
    //   - validateUser.id: 'validateUser' (add) = 1个
    //   - validateUser.name: '验证用户信息' (add) = 1个
    //   - validateUser.type: 'rpc' (add) = 1个
    //   - validateUser.service: '/api/user/validate' (add) = 1个
    const DV: E2ESchema = {
      ...CV,
      model: {
        code: `var UserForm = {}; var validateForm = function() {};`,
        codeTS: `interface UserForm {
  username: string;
  email: string;
  avatar?: string;
}

// AI生成的验证逻辑
const validateForm = (form: UserForm): string[] => {
  const errors: string[] = [];

  if (!form.username || form.username.length < 3) {
    errors.push('用户名至少3个字符');
  }

  if (!form.email || !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(form.email)) {
    errors.push('请输入有效的邮箱地址');
  }

  return errors;
};`,
      },
      apis: [
        ...(CV.apis || []),
        {
          id: 'validateUser',
          name: '验证用户信息',
          type: 'rpc',
          service: '/api/user/validate',
        },
      ],
    }

    it('📊 验证 CV → DV 变更数量 (AI修改D)', () => {
      const diff = engine.diff(CV, DV)

      console.log('\n=== CV → DV 变更验证 ===')
      console.log('实际变更数:', diff.stats.total)
      console.log('按字段分布:', diff.stats.byField)
      console.log('按策略分布:', diff.stats.byStrategy)

      expect(diff.stats.total).toBe(15)
      expect(diff.stats.byField.model || 0).toBe(11) // Model变更
      expect(diff.stats.byField.apis || 0).toBe(4) // APIs变更
    })

    // EV：人工优化提示
    // 手动计算变更数：DV → EV = 6个变更
    // View 变更(6个)：
    //   - errorMessage 组件: type(add), name(add), props.type(add), props.showIcon(add), props.closable(add), _parent_(add) = 6个
    const EV: E2ESchema = {
      ...DV,
      view: {
        ...DV.view,
        children: [
          ...(DV.view.children || []),
          {
            id: 'errorMessage',
            type: 'Alert',
            name: '错误提示',
            props: {
              type: 'error',
              showIcon: true,
              closable: true,
            },
          },
        ],
      },
    }

    it('📊 验证 DV → EV 变更数量 (人工修改E)', () => {
      const diff = engine.diff(DV, EV)

      console.log('\n=== DV → EV 变更验证 ===')
      console.log('实际变更数:', diff.stats.total)
      console.log('按字段分布:', diff.stats.byField)
      console.log('按策略分布:', diff.stats.byStrategy)

      expect(diff.stats.total).toBe(6)
      expect(diff.stats.byField.view || 0).toBe(6) // View变更
    })

    it('🧮 应该正确计算AI覆盖率', () => {
      // 计算逻辑说明：
      // 1. alphaDiff = diff(initV, EV) - 整个变更的总修改量
      // 2. ADiff = diff(initV, AV) - AI修改A的贡献
      // 3. DDiff = diff(CV, DV) - AI修改D的贡献
      // 4. 通过消耗式匹配计算 (ADiff + DDiff) 在 alphaDiff 中的占比
      //
      // 关键计算规则：
      // - ViewDiffStrategy 会生成父子关系(_parent_) 的 ChangeUnit
      // - ConfigDiffStrategy 把 props 的每个属性作为独立 unit
      // - PrimitiveValueDiffStrategy 把 type 和 name 作为独立的变更单位
      // - CodeLineDiffStrategy 按行比较，忽略空行，相同内容不产生 change
      // - 消耗式匹配：相同 ID 的 unit 按值匹配，完全相同不产生 change

      // Step 1: 计算整个变更的总修改量
      const alphaDiff = engine.diff(initV, EV)
      console.log('📊 整个变更总修改量:', alphaDiff.stats.total)

      // Step 2: 计算AI修改A的贡献
      const ADiff = engine.diff(initV, AV)
      console.log('🤖 AI修改A贡献:', ADiff.stats.total)

      // Step 3: 计算AI修改D的贡献
      const DDiff = engine.diff(CV, DV)
      console.log('🤖 AI修改D贡献:', DDiff.stats.total)

      // Step 4: 计算AI覆盖率
      const coverage = CoverageCalculator.calculateAICoverage(alphaDiff, [
        ADiff,
        DDiff,
      ])
      console.log('📈 AI覆盖率:', coverage.toFixed(2) + '%')

      // 验证结果合理性
      expect(alphaDiff.stats.total).toBeGreaterThan(0)
      expect(ADiff.stats.total).toBeGreaterThan(0)
      expect(DDiff.stats.total).toBeGreaterThan(0)
      expect(coverage).toBeGreaterThan(0)
      expect(coverage).toBeLessThanOrEqual(100)

      // 预期覆盖率：24/38 = 63.16%
      // 这个值反映了正确的变更统计逻辑（只计算真实的内容变更）
      expect(coverage).toBeCloseTo(63.16, 1)
    })

    it('🔍 详细覆盖率分析', () => {
      const totalDiff = engine.diff(initV, EV)
      const aiDiffA = engine.diff(initV, AV)
      const aiDiffD = engine.diff(CV, DV)

      const detailed = CoverageCalculator.calculateDetailedCoverage(totalDiff, [
        aiDiffA,
        aiDiffD,
      ])

      // 验证详细统计信息结构完整
      expect(detailed.totalCoverage).toBeGreaterThan(0)
      expect(detailed.preservedChanges.length).toBeGreaterThan(0)
      expect(detailed.byType).toBeDefined()
      expect(detailed.byField).toBeDefined()
      expect(detailed.byStrategy).toBeDefined()
    })

    it('🎯 验证策略类型覆盖', () => {
      const totalDiff = engine.diff(initV, EV)
      const strategies = new Set(totalDiff.changes.map((c) => c.unit.strategy))

      // 验证包含多种策略类型
      expect(strategies.size).toBeGreaterThan(1)

      // 验证包含视图相关策略
      const hasViewStrategy = Array.from(strategies).some(
        (s) =>
          s.includes('VIEW') || s.includes('Config') || s.includes('Primitive'),
      )
      expect(hasViewStrategy).toBe(true)
    })
  })

  describe('📖 边界情况', () => {
    it('🚫 空变更返回0%覆盖率', () => {
      const emptyDiff = {
        changes: [],
        stats: {
          total: 0,
          byType: { add: 0, modify: 0, delete: 0 },
          byField: {},
          byStrategy: {},
        },
      }

      const coverage = CoverageCalculator.calculateAICoverage(emptyDiff, [])
      expect(coverage).toBe(0)
    })

    it('💯 完全匹配返回100%覆盖率', () => {
      const testDiff = {
        changes: [
          {
            type: 'add' as const,
            unit: {
              id: 'test1',
              value: 'value1',
              target: { type: 'test', fieldPath: 'test' },
              path: 'path1',
              strategy: 'test',
            },
            path: 'path1',
          },
        ],
        stats: {
          total: 1,
          byType: { add: 1, modify: 0, delete: 0 },
          byField: {},
          byStrategy: {},
        },
      }

      const coverage = CoverageCalculator.calculateAICoverage(testDiff, [
        testDiff,
      ])
      expect(coverage).toBe(100)
    })

    it('🔄 消耗式匹配机制', () => {
      const finalDiff = {
        changes: [
          {
            type: 'add' as const,
            unit: {
              id: 'same-id',
              value: 'final-value',
              target: { type: 'test', fieldPath: 'test' },
              path: 'path1',
              strategy: 'test',
            },
            path: 'path1',
          },
          {
            type: 'modify' as const,
            unit: {
              id: 'same-id',
              value: 'another-value',
              target: { type: 'test', fieldPath: 'test' },
              path: 'path2',
              strategy: 'test',
            },
            path: 'path2',
          },
        ],
        stats: {
          total: 2,
          byType: { add: 1, modify: 1, delete: 0 },
          byField: {},
          byStrategy: {},
        },
      }

      const aiDiff = {
        changes: [
          {
            type: 'add' as const,
            unit: {
              id: 'same-id',
              value: 'final-value', // 匹配
              target: { type: 'test', fieldPath: 'test' },
              path: 'path1',
              strategy: 'test',
            },
            path: 'path1',
          },
          {
            type: 'modify' as const,
            unit: {
              id: 'same-id',
              value: 'different-value', // 不匹配
              target: { type: 'test', fieldPath: 'test' },
              path: 'path3',
              strategy: 'test',
            },
            path: 'path3',
          },
        ],
        stats: {
          total: 2,
          byType: { add: 1, modify: 1, delete: 0 },
          byField: {},
          byStrategy: {},
        },
      }

      const coverage = CoverageCalculator.calculateAICoverage(finalDiff, [
        aiDiff,
      ])
      expect(coverage).toBe(50) // 只有一个匹配
    })
  })
})
