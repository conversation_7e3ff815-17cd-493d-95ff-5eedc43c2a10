# E2E Schema 序列化引擎：最小可追踪变更单元的理论与实践

## 背景与问题

在现代低代码开发平台中，Schema 作为应用程序的核心描述语言，承载着从界面布局到业务逻辑的完整信息。随着 AI 辅助开发的兴起，如何精确量化 AI 在开发过程中的贡献度成为了一个关键问题。

### 核心挑战

传统的代码差异分析工具（如 Git diff）主要面向文本文件，无法有效处理 Schema 这种结构化数据的语义变更。具体表现在：

1. **结构复杂性**：E2E Schema 包含组件树、API 配置、代码片段、联动逻辑等多种异构数据结构
2. **嵌套深度**：组件可以无限嵌套，每个组件又包含 props、effect、apis、if 等多层嵌套字段
3. **语义识别**：相同的内容在不同位置可能具有不同的语义意义
4. **变更追踪**：需要区分真实的业务变更和由于结构调整产生的伪变更

### 实际场景

考虑一个典型的开发流程：开发者从初始版本 `initV` 开始，经过 AI 修改 A、人工修改 B、人工修改 C、AI 修改 D、人工修改 E，最终得到版本 `EV`。如何准确计算 AI 修改 A 和 D 在整个变更中的贡献占比？

这个看似简单的问题，实际上涉及到复杂的结构化数据差异分析和匹配算法。

## 解决方案：最小可追踪变更单元

### 核心理念

我们提出了"最小可追踪变更单元"（Minimal Trackable Change Unit）的概念，将所有 Schema 变更分解为原子级的、可独立追踪的最小单位。

**理论基础**：每个变更单元都应当与大运河平台上的一个具体可视化操作一一对应。例如：
- 修改组件的 `type` 属性 → 在设计器中切换组件类型
- 添加一个子组件 → 在设计器中拖拽新组件到容器中
- 修改 API 的 `service` 地址 → 在 API 配置面板中更新接口地址

### 变更单元的结构

每个变更单元包含以下核心信息：

```typescript
interface ChangeUnit {
  // 核心字段
  id: string           // 唯一标识符
  target: ChangeTarget // 变更目标（用于跨版本匹配）
  value: unknown       // 实际值

  // 附属字段
  path: string         // 在 Schema 中的路径
  strategy: string     // 产生此单元的策略
  metadata?: Record<string, unknown> // 元数据
}
```

### 变更目标的意义

`target` 字段是变更单元的核心，它定义了变更的"身份"：

```typescript
interface ChangeTarget {
  type: string         // 目标类型（如 'VIEW_COMPONENT_PROPERTY'）
  fieldPath: string    // 字段路径（如 'view'）
  componentId: string  // 组件ID（如 'usernameInput'）
  attributePath: string // 属性路径（如 'props.placeholder'）
}
```

**变更目标的意义**：它回答了"这个变更是针对什么的？"这个问题。通过 `componentId + attributePath` 的组合，我们可以在不同版本的 Schema 中准确定位到同一个逻辑实体，即使它们在结构中的位置发生了变化。

### 变更值的意义

`value` 字段记录了变更的具体内容，它回答了"变更成了什么？"这个问题。结合新旧版本的 `value` 对比，我们可以判断这是一个添加、修改还是删除操作。

## 变更单元示例

### 简单属性变更

```typescript
// Schema 变更：将用户名输入框的占位符从 "请输入用户名" 改为 "请输入您的用户名"
const changeUnit: ChangeUnit = {
  id: "view_usernameInput_props.placeholder_CONFIG_DIFF_STRATEGY",
  target: {
    type: "VIEW_COMPONENT_PROPERTY",
    fieldPath: "view",
    componentId: "usernameInput",
    attributePath: "props.placeholder"
  },
  path: "view.usernameInput.props.placeholder",
  value: "请输入您的用户名",
  strategy: "ViewDiffStrategy"
}
```

### 复杂嵌套变更

```typescript
// Schema 变更：修改组件的 effect 代码
const changeUnit: ChangeUnit = {
  id: "view_usernameInput_effect.codeTS_CODE_LINE_DIFF_STRATEGY",
  target: {
    type: "VIEW_COMPONENT_EFFECT_CODE",
    fieldPath: "view",
    componentId: "usernameInput", 
    attributePath: "effect.codeTS"
  },
  path: "view.usernameInput.effect.codeTS",
  value: "console.log('用户名输入框已挂载');",
  strategy: "ViewDiffStrategy"
}
```

## 差异计算实例

### 场景设置

假设我们有两个 Schema 版本：

**旧版本**：
```typescript
const oldSchema = {
  view: {
    id: 'form',
    type: 'Form',
    children: [{
      id: 'usernameInput',
      type: 'Input',
      props: { placeholder: '请输入用户名' }
    }]
  }
}
```

**新版本**：
```typescript
const newSchema = {
  view: {
    id: 'form', 
    type: 'Form',
    children: [
      {
        id: 'avatarUpload',
        type: 'Upload',
        props: { accept: 'image/*' }
      },
      {
        id: 'usernameInput',
        type: 'Input', 
        props: { placeholder: '请输入您的用户名' }
      }
    ]
  }
}
```

### 变更单元提取

系统会提取出以下变更单元：

1. **新增头像上传组件**：
   - `avatarUpload.type` (add)
   - `avatarUpload.props.accept` (add)
   - `avatarUpload._parent_` (add)

2. **修改用户名占位符**：
   - `usernameInput.props.placeholder` (modify)

3. **位置关系变更**：
   - `usernameInput._parent_` (modify) - 反映在父容器中的位置变化

### 差异匹配算法

通过"消耗式匹配"算法，系统能够准确识别：
- 哪些是真正的内容变更
- 哪些是由于结构调整产生的位置变更
- 如何避免重复计算同一个变更

## 技术实现深度解析

### 面临的技术难题

1. **树结构标识问题**：如何在组件树中稳定地标识每个节点？
2. **嵌套字段处理**：如何统一处理 effect、apis、if 等不同类型的嵌套字段？
3. **路径变化处理**：如何区分真实变更和路径变化？
4. **性能优化**：如何在大型 Schema 中高效地进行差异计算？

### 顶层技术方案

我们采用了**策略模式 + 通用嵌套范式**的架构设计：

```
SchemaDiffEngine (差异引擎)
├── FieldDiffStrategy (字段策略接口)
│   ├── BaseFieldDiffStrategy (抽象基类)
│   ├── NestedFieldStrategy (通用嵌套策略)
│   └── 具体策略实现
└── 消耗式匹配算法
```

### 核心策略实现

#### 1. ViewDiffStrategy - 组件树处理

**挑战**：组件树是动态的树形结构，组件可以移动、添加、删除，如何稳定地追踪每个组件的变更？

**解决方案**：基于组件ID的语义标识 + BFS遍历

```typescript
// 错误的路径依赖方式
target: { path: "view.children[0].props.placeholder" } // ❌ 索引会变化

// 正确的语义标识方式  
target: { 
  componentId: "usernameInput",           // ✅ 稳定的组件标识
  attributePath: "props.placeholder"      // ✅ 属性路径
}
```

**关键创新**：
- 使用 `componentId + attributePath` 替代传统的路径索引
- 引入 `_parent_` 特殊属性来追踪位置关系变更
- 通过 BFS 遍历确保处理顺序的一致性

#### 2. 通用嵌套范式 - 统一嵌套处理

**挑战**：不同字段有不同的嵌套结构（对象嵌套、数组嵌套、树嵌套），如何统一处理？

**解决方案**：声明式配置 + 策略组合

```typescript
// View 字段的嵌套配置示例
const VIEW_NESTED_CONFIG = NestedConfigBuilder.forTree({
  childrenField: 'children',
  idField: 'id',
  nodeConfig: {
    // 基础属性
    type: NestedConfigBuilder.simple('type', 'ConfigDiffStrategy'),
    name: NestedConfigBuilder.simple('name', 'ConfigDiffStrategy'),

    // Effect 字段 - 对象嵌套
    effect: NestedConfigBuilder.forObject({
      type: NestedConfigBuilder.simple('type', 'ConfigDiffStrategy'),
      code: NestedConfigBuilder.simple('code', 'CodeLineDiffStrategy'),
      codeTS: NestedConfigBuilder.simple('codeTS', 'CodeLineDiffStrategy'),
      codeES: NestedConfigBuilder.simple('codeES', 'CodeLineDiffStrategy')
    }),

    // APIs 字段 - 数组嵌套
    apis: NestedConfigBuilder.forArray({
      strategy: 'APIDiffStrategy'
    }),

    // If 字段 - 对象嵌套（与 effect 相同结构）
    if: NestedConfigBuilder.forObject({
      type: NestedConfigBuilder.simple('type', 'ConfigDiffStrategy'),
      code: NestedConfigBuilder.simple('code', 'CodeLineDiffStrategy'),
      codeTS: NestedConfigBuilder.simple('codeTS', 'CodeLineDiffStrategy'),
      codeES: NestedConfigBuilder.simple('codeES', 'CodeLineDiffStrategy')
    })
  }
})
```

**技术突破**：
- **配置驱动**：新增嵌套字段只需配置，无需编码
- **策略复用**：相同结构的字段（如 effect 和 if）共享配置
- **类型安全**：完整的 TypeScript 类型推导

#### 3. ConfigDiffStrategy - 配置对象处理

**挑战**：配置对象可能包含原始值、嵌套对象、数组等多种类型，如何统一处理？

**解决方案**：递归分解 + 特殊键标识

```typescript
// 处理原始值
const primitiveValue = "用户名输入框"
// 生成变更单元：{ path: "_primitive_", value: "用户名输入框" }

// 处理对象
const objectValue = { placeholder: "请输入用户名", type: "text" }
// 生成变更单元：
// - { path: "placeholder", value: "请输入用户名" }
// - { path: "type", value: "text" }
```

#### 4. CodeLineDiffStrategy - 代码差异处理

**挑战**：代码字符串的变更如何进行细粒度追踪？

**解决方案**：行级差异 + 内容哈希

```typescript
// 旧代码
const oldCode = `console.log("hello");
console.log("world");`

// 新代码
const newCode = `console.log("hello");
console.log("universe");
console.log("!");`

// 生成变更单元：
// - Line 1: 无变化（不生成单元）
// - Line 2: modify - "console.log("world");" → "console.log("universe");"
// - Line 3: add - "console.log("!");"
```

### 消耗式匹配算法

这是整个系统的核心算法，类似于"连连看"游戏：

```typescript
function consumptiveMatch(finalChanges: ChangeUnit[], aiChanges: ChangeUnit[]): number {
  const availableAiChanges = [...aiChanges] // 可消耗的AI变更池
  let matchedCount = 0

  for (const finalChange of finalChanges) {
    // 寻找相同ID的AI变更
    const matchIndex = availableAiChanges.findIndex(
      aiChange => aiChange.id === finalChange.id
    )

    if (matchIndex !== -1) {
      // 找到匹配，消耗这个AI变更
      availableAiChanges.splice(matchIndex, 1)
      matchedCount++
    }
  }

  return matchedCount / finalChanges.length * 100 // 覆盖率百分比
}
```

**算法特点**：
- **唯一匹配**：每个变更单元只能被匹配一次
- **精确计算**：避免重复计算和误判
- **公平性**：不同类型的变更具有相同的权重

### 实际运行示例

让我们通过一个真实的测试用例来展示系统的工作过程：

#### 场景：用户注册表单开发

**初始版本 (initV)**：
```typescript
{
  view: {
    id: 'registerForm',
    type: 'Form',
    children: [
      { id: 'usernameInput', type: 'Input', props: { placeholder: '请输入用户名' } },
      { id: 'emailInput', type: 'Input', props: { placeholder: '请输入邮箱' } }
    ]
  }
}
```

**AI修改A后 (AV)** - 添加头像上传：
```typescript
{
  view: {
    id: 'registerForm',
    type: 'Form',
    children: [
      { id: 'avatarUpload', type: 'Upload', props: { accept: 'image/*' } }, // 新增
      { id: 'usernameInput', type: 'Input', props: { placeholder: '请输入用户名' } },
      { id: 'emailInput', type: 'Input', props: { placeholder: '请输入邮箱' } }
    ]
  }
}
```

**系统分析结果**：
```
=== initV → AV 变更分析 ===
总变更数: 9
变更详情:
1. avatarUpload.type (add) - "Upload"
2. avatarUpload.props.accept (add) - "image/*"
3. avatarUpload._parent_ (add) - 父容器信息
4. usernameInput._parent_ (modify) - 位置从[0]变为[1]
5. emailInput._parent_ (modify) - 位置从[1]变为[2]
... (其他变更)

AI覆盖率: 100% (这是纯AI修改)
```

#### 最终覆盖率计算

经过完整的开发流程后：
```
=== 最终覆盖率分析 ===
总变更数 (initV → EV): 38
AI修改A贡献: 9个变更单元
AI修改D贡献: 15个变更单元
AI总贡献: 24个变更单元
AI覆盖率: 63.16%
```

这个结果准确反映了AI在整个开发过程中的实际贡献度，为AI辅助开发的效果评估提供了量化依据。
